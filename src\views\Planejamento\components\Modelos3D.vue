<template>
  <div class="tratamento-content">
    <div class="row border-between">
      <div class="col-12">
        <div class="modelo3d-group-container"
          v-show="pendingModeloPreviews.length === 0"
          :class="{ 'drag-over': dragOverContainer }"
          @dragover.prevent="onDragOverContainer"
          @dragleave.prevent="onDragLeaveContainer"
          @drop.prevent="onDropContainer"
        >
          <!-- Indicador de carregamento -->
          <div v-if="isLoadingModelos" class="loading-models-container">
            <div class="loading-spinner">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
              </div>
            </div>
            <p class="loading-text">Carregando modelos 3D...</p>
          </div>

          <!-- Centralized no-models message -->
          <div v-else-if="safePatientModelos.length === 0" class="no-models-message">
            <div class="message-content">
              <font-awesome-icon :icon="['fas', 'info-circle']" class="me-2" size="lg" />
              <p class="mb-0">
                Aqui você pode armazenar os modelos 3D do paciente.<br>
                Envie arquivos STL para visualização.
              </p>
            </div>
          </div>

          <!-- Agrupado por data -->
          <template v-else-if="groupByDate">
            <div v-for="group in groupedModelosByDate" :key="group.date" class="date-group">
              <div class="date-group-header">
                <div class="date-group-header-content">
                  <span class="date-how-much">{{ $filters.howMuchTime(group.date, { type: 'date' }) }}</span>
                  <span class="date-text">{{ $filters.dateLong(group.date) }}</span>
                </div>
                <div class="date-group-actions">
                  <button
                    class="date-delete-all-btn"
                    @click.stop="confirmarExcluirTodosModelos(group.date)"
                    title="Excluir todos os modelos desta data"
                  >
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </div>
              <div class="models-container w-100">
                <div v-for="modelo in group.modelos" :key="modelo.url" class="modelo-card"
                  draggable="true"
                  @dragstart="onModeloDragStart($event, modelo)"
                >
                  <div class="modelo-preview" @click="openModelViewer(modelo)">
                    <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                    <div class="modelo-info">
                      <span class="modelo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                    </div>
                    <button
                      class="modelo-delete-btn"
                      @click.stop="confirmarExcluirModelo(modelo)"
                      title="Excluir este modelo"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Exibição com grupos -->
          <template v-else-if="showGroups && modelosAgrupados.length > 0">
            <div class="models-container w-100">
              <div v-for="item in modelosAgrupados" :key="item.tipo === 'grupo' ? item.grupo_exame_id : item.modelo.id" class="modelo-item">

                <!-- Grupo de modelos -->
                <div v-if="item.tipo === 'grupo'" class="grupo-card"
                  draggable="true"
                  @dragstart="onGrupoDragStart($event, item)"
                >
                  <div class="grupo-header" @click="toggleGrupoExpanded(item.grupo_exame_id)">
                    <div class="grupo-info">
                      <div class="grupo-icon">
                        <font-awesome-icon :icon="['fas', 'cubes']" size="2x" />
                      </div>
                      <div class="grupo-details">
                        <span class="grupo-name">{{ item.grupo_nome }}</span>
                        <span class="grupo-date">{{ $filters.dateDmy(item.data) }}</span>
                        <span class="grupo-count">{{ item.modelos.length }} modelo{{ item.modelos.length > 1 ? 's' : '' }}</span>
                      </div>
                    </div>
                    <div class="grupo-actions">
                      <button
                        class="grupo-expand-btn"
                        :class="{ 'expanded': isGrupoExpanded(item.grupo_exame_id) }"
                        title="Expandir/Recolher grupo"
                      >
                        <i class="fas fa-chevron-down"></i>
                      </button>
                      <button
                        class="grupo-view-btn"
                        @click.stop="openGrupoViewer(item)"
                        title="Visualizar grupo completo"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                      <button
                        class="grupo-delete-btn"
                        @click.stop="confirmarExcluirGrupo(item)"
                        title="Excluir grupo completo"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Modelos do grupo (expansível) -->
                  <Transition name="grupo-expand">
                    <div v-if="isGrupoExpanded(item.grupo_exame_id)" class="grupo-modelos">
                      <div v-for="modelo in item.modelos" :key="modelo.id" class="modelo-grupo-item"
                        draggable="true"
                        @dragstart="onModeloDragStart($event, modelo)"
                      >
                        <div class="modelo-grupo-preview" @click="openModelViewer(modelo)">
                          <div class="modelo-grupo-icon">
                            <font-awesome-icon :icon="['fas', 'cube']" size="lg" />
                          </div>
                          <div class="modelo-grupo-info">
                            <span class="modelo-grupo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                            <span class="modelo-grupo-type">{{ formatTipoModelo(modelo.tipo_modelo) }}</span>
                          </div>
                          <div class="modelo-grupo-controls">
                            <button
                              class="modelo-visibility-btn"
                              :class="{ 'visible': isModeloVisible(modelo.id) }"
                              @click.stop="toggleModeloVisibility(modelo.id)"
                              title="Mostrar/Ocultar modelo"
                            >
                              <i :class="isModeloVisible(modelo.id) ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                            </button>
                            <button
                              class="modelo-individual-delete-btn"
                              @click.stop="confirmarExcluirModelo(modelo)"
                              title="Excluir este modelo"
                            >
                              <i class="fas fa-times"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Transition>
                </div>

                <!-- Modelo individual -->
                <div v-else class="modelo-card individual"
                  draggable="true"
                  @dragstart="onModeloDragStart($event, item.modelo)"
                >
                  <div class="modelo-preview" @click="openModelViewer(item.modelo)">
                    <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                    <div class="modelo-info">
                      <span class="modelo-name">{{ item.modelo.descricao || 'Modelo 3D' }}</span>
                      <span class="modelo-date">{{ $filters.dateDmy(item.modelo.data) }}</span>
                    </div>
                    <button
                      class="modelo-delete-btn"
                      @click.stop="confirmarExcluirModelo(item.modelo)"
                      title="Excluir este modelo"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Sem agrupamento (fallback) -->
          <template v-else>
            <div class="models-container w-100">
              <div v-for="modelo in safePatientModelos" :key="modelo.url" class="modelo-card"
                draggable="true"
                @dragstart="onModeloDragStart($event, modelo)"
              >
                <div class="modelo-preview" @click="openModelViewer(modelo)">
                  <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                  <div class="modelo-info">
                    <span class="modelo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                    <span class="modelo-date">{{ $filters.dateDmy(modelo.data) }}</span>
                  </div>
                  <button
                    class="modelo-delete-btn"
                    @click.stop="confirmarExcluirModelo(modelo)"
                    title="Excluir este modelo"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </template>
        </div>
        <Transition>
          <div class="row mt-3" v-show="pendingModeloPreviews.length === 0">
            <div class="col-12 text-center">
              <!-- Toggle para escolher tipo de upload -->
              <div class="upload-type-selector mb-3">
                <div class="btn-group" role="group" aria-label="Tipo de upload">
                  <input type="radio" class="btn-check" name="uploadType" id="individual" value="individual" v-model="uploadType" autocomplete="off">
                  <label class="btn btn-outline-primary" for="individual">
                    <i class="fas fa-cube me-2"></i>Individual
                  </label>

                  <input type="radio" class="btn-check" name="uploadType" id="grupo" value="grupo" v-model="uploadType" autocomplete="off">
                  <label class="btn btn-outline-primary" for="grupo">
                    <i class="fas fa-cubes me-2"></i>Grupo
                  </label>
                </div>
              </div>

              <input
                id="modeloFileInput"
                type="file"
                accept=".stl,.obj,.ply,.3mf"
                multiple
                @change="setModeloPreviews"
                hidden
              />
              <button
                class="btn bg-gradient-primary btn-add-models"
                @click="chooseModeloFile"
              >
                <i class="fas fa-upload me-2"></i>
                {{ uploadType === 'grupo' ? 'Enviar Grupo de Modelos 3D' : 'Enviar Modelos 3D' }}
              </button>
            </div>
          </div>
        </Transition>
        <Transition>
          <div v-if="pendingModeloPreviews.length > 0" class="row mt-3">
            <!-- Campos do grupo (se upload em grupo) -->
            <div v-if="uploadType === 'grupo'" class="col-12 mb-4">
              <div class="grupo-config-card">
                <h6 class="mb-3">
                  <i class="fas fa-cubes me-2"></i>Configuração do Grupo
                </h6>
                <div class="row">
                  <div class="col-md-6">
                    <label class="form-label">Nome do Grupo</label>
                    <input
                      type="text"
                      class="form-control"
                      v-model="grupoConfig.nome"
                      placeholder="Ex: Mandíbula e Maxila"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Data do Exame</label>
                    <input
                      type="date"
                      class="form-control"
                      v-model="grupoConfig.data"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Preview dos modelos -->
            <div class="col-12 d-flex flex-wrap justify-content-center gap-3">
              <div v-for="(_, index) in pendingModeloPreviews" :key="index" class="upload-preview">
                <div class="modelo-preview-icon">
                  <font-awesome-icon :icon="['fas', 'cube']" size="4x" />
                </div>
                <div class="modelo-preview-name">{{ pendingModeloFiles[index].name }}</div>

                <!-- Campos específicos para upload individual -->
                <template v-if="uploadType === 'individual'">
                  <input type="date" class="text-center" v-model="pendingModeloMetadata[index].date" />
                  <input type="text" class="text-center" v-model="pendingModeloMetadata[index].description" placeholder="Descrição" />
                </template>

                <!-- Campos específicos para upload em grupo -->
                <template v-else>
                  <select class="form-select form-select-sm mt-2" v-model="pendingModeloMetadata[index].tipo">
                    <option value="mandibula">Mandíbula</option>
                    <option value="maxila">Maxila</option>
                    <option value="geral">Geral</option>
                    <option value="outro">Outro</option>
                  </select>
                  <input type="text" class="text-center mt-2" v-model="pendingModeloMetadata[index].description" :placeholder="`Descrição do modelo ${index + 1}`" />
                </template>
              </div>
            </div>

            <div class="col-12 text-center mt-3">
              <button
                class="btn btn-sm btn-danger me-2"
                @click="cancelModeloUpload"
              >
                Cancelar
              </button>
              <button
                class="btn btn-sm btn-success"
                @click="confirmModeloUpload"
                :disabled="uploadType === 'grupo' && (!grupoConfig.nome || !grupoConfig.data)"
              >
                {{ uploadType === 'grupo' ? 'Confirmar Upload do Grupo' : 'Confirmar Upload' }}
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Modal para visualização do modelo 3D -->
    <div class="modal modelo3d-modal" id="modelViewerModal" tabindex="-1" aria-labelledby="modelViewerModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modelViewerModalLabel">Visualizador 3D</h5>
            <button type="button" class="btn-close" aria-label="Close"></button>
          </div>
          <div class="modal-body p-0">
            <!-- Novo visualizador 3D -->
            <div style="width: 100%; height: 600px;">
              <Modelos3DViewer
                v-if="currentModeloUrl"
                :modelUrl="currentModeloUrl"
                :pacienteNome="paciente.nome"
                :modeloDescricao="currentModelo?.descricao || 'Modelo 3D'"
                :modeloData="currentModelo?.data || ''"
                @error="handleViewerError"
                @close="closeModelViewer"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para visualização múltipla de modelos 3D -->
    <div class="modal modelo3d-modal" id="multipleModelViewerModal" tabindex="-1" aria-labelledby="multipleModelViewerModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="multipleModelViewerModalLabel">
              <i class="fas fa-cubes me-2"></i>
              Visualizador de Grupo - {{ currentGrupoViewer?.grupoNome || 'Grupo de Modelos' }}
            </h5>
            <button type="button" class="btn-close" aria-label="Close" @click="closeMultipleModelViewer"></button>
          </div>
          <div class="modal-body p-0">
            <!-- Visualizador múltiplo de modelos 3D -->
            <div style="width: 100%; height: 600px;">
              <Modelos3DViewerMultiplo
                v-if="showMultipleViewer && currentGrupoViewer"
                :modelos="currentGrupoViewer.modelos"
                :pacienteNome="currentGrupoViewer.pacienteNome"
                :grupoNome="currentGrupoViewer.grupoNome"
                :grupoData="currentGrupoViewer.grupoData"
                @error="handleMultipleViewerError"
                @close="closeMultipleModelViewer"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Zona de exclusão para modelos arrastados -->
    <div
      class="modelo-delete-zone"
      :class="{ 'active': isDraggingModelo, 'highlight': isOverDeleteZone }"
      ref="deleteZone"
      @dragover.prevent="onDragOverDeleteZone"
      @dragleave.prevent="onDragLeaveDeleteZone"
      @drop.prevent="onDropOnDeleteZone"
    >
      <div class="delete-zone-content">
        <i class="fas fa-trash"></i>
        <span>Solte aqui para excluir</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.models-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
  min-height: 125px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

/* Estilo para o container de modelos quando não está agrupado por data */
template:not([v-else-if="groupByDate"]) .models-container {
  border-radius: 8px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.modelo-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 160px;
  height: 160px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.modelo-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 15px;
  text-align: center;
  position: relative;
}

.modelo-icon {
  color: #0d6efd;
  margin-bottom: 10px;
}

.modelo-info {
  display: flex;
  flex-direction: column;
}

.modelo-delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.85);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 11px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  z-index: 10;
  opacity: 0;
  transform: scale(0.8);
}

.modelo-preview:hover .modelo-delete-btn {
  opacity: 1;
  transform: scale(1);
}

.modelo-delete-btn:hover {
  background-color: #dc3545;
  transform: scale(1.1);
}

.modelo-name {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 5px;
  word-break: break-word;
}

.modelo-desc, .modelo-date {
  font-size: 0.8rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.modelo-date {
  color: #0d6efd;
  font-size: 0.75rem;
  margin-top: 2px;
}

.modelo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.modelo3d-group-container {
  background: transparent;
  min-height: 300px;
  max-height: calc(100svh - 700px);
  overflow-y: auto;
  transition: all 0.3s ease;
  position: relative;
  padding: 0;
  margin: 0 auto;
  max-width: 1400px;
  border: none;
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
}

.modelo3d-group-container::-webkit-scrollbar {
  width: 8px;
}

.modelo3d-group-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.modelo3d-group-container::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 10px;
  border: 2px solid transparent;
}

.modelo3d-group-container.drag-over {
  border: 2px dashed #0d6efd;
  background-color: rgba(13, 110, 253, 0.03);
  border-radius: 12px;
}

.date-group {
  margin-bottom: 20px;
}

.date-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: #e9ecef;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
}

.date-group-header-content {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.date-text {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-weight: 500;
}

.date-group-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1;
}

.date-delete-all-btn {
  background-color: transparent;
  color: #dc3545;
  border: none;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.date-delete-all-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  opacity: 1;
}

.date-how-much {
  font-weight: 600;
  color: #495057;
  font-size: 0.85rem;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 4px 10px;
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s ease;
  border: 1px solid rgba(13, 110, 253, 0.15);
  z-index: 1;
}

.no-models-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
}

.message-content {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.loading-models-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
}

.loading-spinner {
  margin-bottom: 20px;
}

.loading-text {
  font-size: 1rem;
  font-weight: 500;
  color: #0d6efd;
}

.btn-add-models {
  margin-top: 10px;
}

.upload-preview {
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 5px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
}

.modelo-preview-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  color: #0d6efd;
}

.modelo-preview-name {
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 10px;
  word-break: break-word;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.upload-preview input[type="date"],
.upload-preview input[type="text"] {
  width: 100%;
  box-sizing: border-box;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.stl-viewer {
  width: 100%;
  height: 500px;
  background: #f0f0f0;
  transition: opacity 0.3s ease;
  touch-action: none; /* Importante para evitar problemas de toque em dispositivos móveis */
  outline: none; /* Remover contorno quando o elemento recebe foco */
  cursor: grab; /* Cursor indicando que o objeto pode ser arrastado */
}

.stl-viewer:active {
  cursor: grabbing; /* Cursor quando está arrastando */
}

.stl-viewer.hidden {
  opacity: 0;
}

.stl-loader-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.8);
  z-index: 10;
}

.stl-loader-spinner {
  margin-bottom: 20px;
}

.stl-loader-progress {
  width: 80%;
  max-width: 400px;
  text-align: center;
}

.stl-loader-text {
  margin-top: 10px;
  font-weight: 500;
  color: #0d6efd;
}

.progress {
  height: 10px;
  border-radius: 5px;
  background-color: #e9ecef;
  margin-bottom: 5px;
}

.viewer-controls {
  position: absolute;
  bottom: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls-group {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.horizontal-controls {
  display: flex;
  gap: 5px;
}

.control-btn {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #555;
}

.control-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.control-btn:active {
  transform: translateY(0);
  background-color: #e8e8e8;
}

.reset-btn {
  background-color: #e8f4ff;
  color: #0d6efd;
}

.reset-btn:hover {
  background-color: #d1e7ff;
}

.zoom-controls, .view-controls {
  flex-direction: row;
  gap: 5px;
}

.viewer-instructions {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 10;
  pointer-events: none;
}

.instructions-content {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 0.8rem;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  line-height: 1.5;
}

.modelo-delete-zone {
  position: fixed;
  bottom: -100px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 2px dashed #dc3545;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
}

.modelo-delete-zone.active {
  bottom: 20px;
  opacity: 1;
  pointer-events: auto;
}

.modelo-delete-zone.highlight {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  transform: translateX(-50%) scale(1.05);
}

.delete-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #dc3545;
}

.delete-zone-content i {
  font-size: 24px;
  margin-bottom: 5px;
}

/* Estilos para o modal personalizado */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  display: none;
  transition: opacity 0.15s linear;
  opacity: 0;
}

.modal.show {
  display: block;
  opacity: 1;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
  max-width: 800px;
  margin: 1.75rem auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  opacity: 0;
  transition: opacity 0.15s linear;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.modal-xl {
  max-width: 1140px;
}

/* Estilos para o modal do visualizador 3D */
.modelo3d-modal .modal-dialog {
  max-width: 90%;
}

.modelo3d-modal .modal-content {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modelo3d-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  padding: 12px 20px;
}

.modelo3d-modal .modal-title {
  font-weight: 600;
  color: #333;
}

.modelo3d-modal .btn-close {
  background-color: #e9ecef;
  border-radius: 50%;
  padding: 8px;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.modelo3d-modal .btn-close:hover {
  opacity: 1;
  background-color: #dee2e6;
  transform: rotate(90deg);
}

/* Garantir que o modal fique acima do overlay de tela cheia */
.modal {
  z-index: 1200 !important;
}

.modal-backdrop {
  z-index: 1100 !important;
}

/* Estilo para o elemento fantasma durante o arrasto */
.drag-ghost {
  background-color: rgba(13, 110, 253, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Estilos para interface de agrupamento */
.upload-type-selector {
  margin-bottom: 1rem;
}

.upload-type-selector .btn-group {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.upload-type-selector .btn {
  border: none;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.upload-type-selector .btn-check:checked + .btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.grupo-config-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.grupo-config-card h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 15px;
}

.grupo-config-card .form-label {
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 5px;
}

.grupo-config-card .form-control {
  border: 1px solid #ced4da;
  border-radius: 8px;
  padding: 10px 12px;
  transition: all 0.3s ease;
}

.grupo-config-card .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.upload-preview .form-select {
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 6px 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.upload-preview .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Estilos para grupos de modelos */
.modelo-item {
  width: 100%;
  margin-bottom: 15px;
}

.grupo-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.grupo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.grupo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.grupo-header:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

.grupo-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.grupo-icon {
  color: #667eea;
  opacity: 0.8;
}

.grupo-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.grupo-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2d3748;
}

.grupo-date {
  font-size: 0.875rem;
  color: #718096;
}

.grupo-count {
  font-size: 0.8rem;
  color: #a0aec0;
  font-style: italic;
}

.grupo-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.grupo-expand-btn,
.grupo-view-btn,
.grupo-delete-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.grupo-expand-btn {
  background-color: #e2e8f0;
  color: #4a5568;
}

.grupo-expand-btn:hover {
  background-color: #cbd5e0;
  transform: scale(1.05);
}

.grupo-expand-btn.expanded {
  transform: rotate(180deg);
  background-color: #667eea;
  color: white;
}

.grupo-view-btn {
  background-color: #48bb78;
  color: white;
}

.grupo-view-btn:hover {
  background-color: #38a169;
  transform: scale(1.05);
}

.grupo-delete-btn {
  background-color: #f56565;
  color: white;
}

.grupo-delete-btn:hover {
  background-color: #e53e3e;
  transform: scale(1.05);
}

.grupo-modelos {
  border-top: 1px solid #e2e8f0;
  background-color: #f7fafc;
  padding: 15px 20px;
}

.modelo-grupo-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.modelo-grupo-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.modelo-grupo-preview {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
}

.modelo-grupo-icon {
  color: #667eea;
  margin-right: 12px;
}

.modelo-grupo-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.modelo-grupo-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.9rem;
}

.modelo-grupo-type {
  font-size: 0.8rem;
  color: #718096;
  text-transform: capitalize;
}

.modelo-grupo-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.modelo-visibility-btn,
.modelo-individual-delete-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.8rem;
}

.modelo-visibility-btn {
  background-color: #e2e8f0;
  color: #4a5568;
}

.modelo-visibility-btn.visible {
  background-color: #48bb78;
  color: white;
}

.modelo-visibility-btn:hover {
  transform: scale(1.1);
}

.modelo-individual-delete-btn {
  background-color: #fed7d7;
  color: #e53e3e;
}

.modelo-individual-delete-btn:hover {
  background-color: #f56565;
  color: white;
  transform: scale(1.1);
}

.modelo-card.individual {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.modelo-card.individual:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

/* Animações */
.grupo-expand-enter-active,
.grupo-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.grupo-expand-enter-from,
.grupo-expand-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.grupo-expand-enter-to,
.grupo-expand-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>

<script>
import { uploadModelo3D, getModelos3D, excluirModelo3D, uploadGrupoModelos3D, getModelosAgrupados, excluirGrupoModelos3D } from "@/services/modelos3dService";
import cSwal from "@/utils/cSwal.js";
import Modelos3DViewer from "./Modelos3DViewer.vue";
import Modelos3DViewerMultiplo from "./Modelos3DViewerMultiplo.vue";

export default {
  name: "Modelos3D",
  components: {
    Modelos3DViewer,
    Modelos3DViewerMultiplo
  },
  props: {
    paciente: {
      type: Object,
      default: () => ({ modelos3d: [] }),
    },
    groupByDate: {
      type: Boolean,
      default: false
    },
    showGroups: {
      type: Boolean,
      default: true
    }
  },
  emits: ["pacienteChange"],
  setup() {
    return {};
  },
  data() {
    return {
      pendingModeloFiles: [],
      pendingModeloPreviews: [],
      pendingModeloMetadata: [],
      dragOverContainer: false,
      isDraggingModelo: false,
      isOverDeleteZone: false,
      currentModeloViewer: null,
      currentModeloUrl: null,
      currentModelo: null, // Modelo atual sendo visualizado
      isLoadingModelo: false,
      loadingProgress: 0,
      modelos3d: [], // Array para armazenar os modelos 3D carregados
      isLoadingModelos: false, // Flag para indicar se os modelos estão sendo carregados

      // Novas variáveis para agrupamento
      uploadType: 'individual', // 'individual' ou 'grupo'
      grupoConfig: {
        nome: '',
        data: new Date().toISOString().split('T')[0] // Data atual
      },
      modelosAgrupados: [], // Array para armazenar modelos agrupados
      gruposExpandidos: [], // Array de IDs de grupos expandidos
      modelosVisiveis: [], // Array de IDs de modelos visíveis no visualizador

      // Visualizador múltiplo
      currentGrupoViewer: null, // Grupo atual sendo visualizado
      showMultipleViewer: false, // Flag para mostrar visualizador múltiplo
    };
  },
  computed: {
    safePatientModelos() {
      // Usar os modelos carregados do estado local em vez de acessar diretamente do paciente
      return this.modelos3d.length > 0 ? this.modelos3d : [];
    },
    groupedModelosByDate() {
      const groups = {};
      this.safePatientModelos.forEach((modelo) => {
        const date = modelo.data || "Sem data";
        if (!groups[date]) {
          groups[date] = [];
        }

        // O backend já formata as URLs dos modelos 3D corretamente
        // Apenas garantir que temos uma URL válida
        const modeloWithUrl = {
          ...modelo,
          url: modelo.url || ''
        };

        groups[date].push(modeloWithUrl);
      });
      // Convert to array of { date, modelos } sorted by date descending
      return Object.keys(groups)
        .map((date) => ({ date, modelos: groups[date] }))
        .sort((a, b) => (a.date < b.date ? 1 : -1));
    },
  },
  methods: {
    getModeloName(modelo) {
      // Extract filename from URL or use description
      if (modelo.url) {
        const urlParts = modelo.url.split('/');
        return urlParts[urlParts.length - 1];
      }
      return modelo.descricao || "Modelo 3D";
    },
    chooseModeloFile() {
      document.getElementById("modeloFileInput").click();
    },
    setModeloPreviews(event) {
      const allowedExtensions = ['.stl', '.obj', '.ply', '.3mf'];
      const files = Array.from(event.target.files).filter(file => {
        const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        return allowedExtensions.includes(extension);
      });

      if (files.length === 0) {
        cSwal.cError("Por favor, selecione arquivos de modelo 3D válidos (STL, OBJ, PLY, 3MF).");
        return;
      }

      this.pendingModeloFiles = files;
      this.pendingModeloPreviews = Array(files.length).fill("placeholder");

      if (this.uploadType === 'individual') {
        this.pendingModeloMetadata = files.map(() => ({
          date: new Date().toISOString().slice(0, 10),
          description: "",
        }));
      } else {
        // Para upload em grupo
        this.pendingModeloMetadata = files.map((file, index) => ({
          tipo: index === 0 ? 'mandibula' : index === 1 ? 'maxila' : 'geral',
          description: `${file.name.replace(/\.[^/.]+$/, "")}`, // Nome do arquivo sem extensão
        }));
      }
    },
    cancelModeloUpload() {
      this.pendingModeloFiles = [];
      this.pendingModeloPreviews = [];
      this.pendingModeloMetadata = [];

      // Limpar dados do grupo
      this.grupoConfig = {
        nome: '',
        data: new Date().toISOString().split('T')[0]
      };

      const fileInput = document.getElementById("modeloFileInput");
      if (fileInput) fileInput.value = "";
    },
    async confirmModeloUpload() {
      if (this.pendingModeloFiles.length === 0) {
        cSwal.cError("Nenhum arquivo selecionado.");
        return;
      }

      if (this.uploadType === 'grupo') {
        return this.confirmGrupoUpload();
      }

      cSwal.loading("Adicionando modelos 3D...");

      try {
        for (let i = 0; i < this.pendingModeloFiles.length; i++) {
          const file = this.pendingModeloFiles[i];
          const modeloData = {
            paciente_id: this.paciente.id,
            modelo: file, // Será renomeado para 'modelo3d' no serviço
            dir: "modelo3d",
            data: this.pendingModeloMetadata[i]?.date || new Date().toISOString().slice(0, 10),
            descricao: this.pendingModeloMetadata[i]?.description || "",
            tipo: "stl",
          };
          await uploadModelo3D(modeloData);
        }
        cSwal.loaded();
        cSwal.cSuccess("Modelos 3D adicionados com sucesso.");
        this.cancelModeloUpload();

        // Recarregar os modelos 3D após o upload
        await this.loadModelos3D();

        this.$emit("pacienteChange");
      } catch (error) {
        cSwal.loaded();
        console.error("Erro no upload de modelos 3D:", error);
        cSwal.cError("Erro inesperado ao adicionar os modelos 3D.");
      }
    },

    async confirmGrupoUpload() {
      if (!this.grupoConfig.nome || !this.grupoConfig.data) {
        cSwal.cError("Por favor, preencha o nome do grupo e a data do exame.");
        return;
      }

      cSwal.loading("Adicionando grupo de modelos 3D...");

      try {
        const grupoData = {
          paciente_id: this.paciente.id,
          grupo_nome: this.grupoConfig.nome,
          data: this.grupoConfig.data,
          modelos: this.pendingModeloFiles,
          tipos_modelos: this.pendingModeloMetadata.map(meta => meta.tipo),
          descricoes: this.pendingModeloMetadata.map(meta => meta.description),
          dir: "grupo_modelos"
        };

        const response = await uploadGrupoModelos3D(grupoData);

        if (response && response.data && response.data.success) {
          cSwal.loaded();
          cSwal.cSuccess(`Grupo "${this.grupoConfig.nome}" criado com sucesso!`);
          this.cancelModeloUpload();

          // Recarregar os modelos 3D após o upload
          await this.loadModelos3D();

          this.$emit("pacienteChange");
        } else {
          throw new Error("Falha no upload do grupo");
        }
      } catch (error) {
        cSwal.loaded();
        console.error("Erro no upload do grupo de modelos 3D:", error);
        cSwal.cError("Erro inesperado ao adicionar o grupo de modelos 3D.");
      }
    },
    onDragOverContainer() {
      this.dragOverContainer = true;
    },
    onDragLeaveContainer() {
      this.dragOverContainer = false;
    },
    onDropContainer(event) {
      this.dragOverContainer = false;
      const dt = event.dataTransfer;
      if (!dt) return;

      const files = Array.from(dt.files).filter(file =>
        file.name.toLowerCase().endsWith('.stl')
      );

      if (files.length === 0) return;

      // Simulate input change event with these files
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));
      const fileInput = document.getElementById('modeloFileInput');
      if (fileInput) {
        fileInput.files = dataTransfer.files;
        // Trigger change event manually
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
      }
    },
    async openModelViewer(modelo) {
      // Initialize modal
      const modalElement = document.getElementById('modelViewerModal');

      // Set current model and URL
      this.currentModelo = modelo;
      this.currentModeloUrl = modelo.url;

      // Adicionar backdrop
      let backdrop = document.querySelector('.modal-backdrop');
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);

        // Forçar reflow para permitir a animação
        backdrop.offsetHeight;

        // Adicionar classe show para animar
        backdrop.classList.add('show');
      }

      // Preparar o modal
      modalElement.style.display = 'block';
      modalElement.setAttribute('aria-modal', 'true');
      modalElement.setAttribute('role', 'dialog');
      document.body.classList.add('modal-open');

      // Forçar reflow para permitir a animação
      modalElement.offsetHeight;

      // Adicionar classe show para animar
      modalElement.classList.add('show');

      // Configurar o botão de fechar
      const closeButton = modalElement.querySelector('.btn-close');
      if (closeButton) {
        // Remover qualquer listener anterior para evitar duplicação
        closeButton.removeEventListener('click', this.closeModelViewer);
        // Adicionar novo listener
        closeButton.addEventListener('click', this.closeModelViewer);
      }

      // Adicionar evento para fechar com ESC
      document.addEventListener('keydown', this.handleEscKey);
    },

    handleViewerError(error) {
      console.error('Erro no visualizador 3D:', error);
      cSwal.cError('Erro ao visualizar o modelo 3D. Por favor, tente novamente.');
      this.closeModelViewer();
    },

    closeModelViewer() {
      const modalElement = document.getElementById('modelViewerModal');

      // Esconder o modal com animação
      modalElement.classList.remove('show');

      // Remover backdrop com animação
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.classList.remove('show');
      }

      // Aguardar a animação terminar antes de remover completamente
      setTimeout(() => {
        modalElement.style.display = 'none';
        modalElement.removeAttribute('aria-modal');
        modalElement.removeAttribute('role');
        document.body.classList.remove('modal-open');

        // Remover backdrop do DOM
        if (backdrop && backdrop.parentNode) {
          backdrop.parentNode.removeChild(backdrop);
        }

        // Limpar a URL e o modelo atual para desmontar o componente visualizador
        this.currentModeloUrl = null;
        this.currentModelo = null;
      }, 150); // 150ms é aproximadamente a duração da animação

      // Remover evento de ESC
      document.removeEventListener('keydown', this.handleEscKey);
    },

    handleEscKey(event) {
      if (event.key === 'Escape') {
        this.closeModelViewer();
      }
    },
    disposeSTLViewer() {
      // Método mantido para compatibilidade, mas não faz nada
      this.currentModeloViewer = null;
    },
    onDragOverDeleteZone() {
      this.isOverDeleteZone = true;
    },
    onDragLeaveDeleteZone() {
      this.isOverDeleteZone = false;
    },
    onDropOnDeleteZone(event) {
      this.isOverDeleteZone = false;
      this.isDraggingModelo = false;

      const dragType = event.dataTransfer.getData('drag-type');

      if (dragType === 'grupo') {
        // Exclusão de grupo
        const grupoId = event.dataTransfer.getData('grupo-id');
        if (grupoId) {
          const grupo = this.modelosAgrupados.find(item =>
            item.tipo === 'grupo' && item.grupo_exame_id === grupoId
          );
          if (grupo) {
            this.confirmarExcluirGrupo(grupo);
          }
        }
      } else {
        // Exclusão de modelo individual
        const modeloId = event.dataTransfer.getData('modelo-id');
        if (modeloId) {
          this.confirmarExcluirModelo({ id: modeloId });
        }
      }
    },

    confirmarExcluirModelo(modelo) {
      if (!modelo || !modelo.id) {
        cSwal.cError("Não foi possível identificar o modelo para exclusão.");
        return;
      }

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        "Tem certeza que deseja excluir este modelo 3D?<br><br>Esta ação não pode ser desfeita.",
        () => this.excluirModelo(modelo.id),
        {
          title: "Excluir modelo 3D",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirModelo(id) {
      cSwal.loading("Excluindo modelo 3D...");

      try {
        const resultado = await excluirModelo3D(id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Modelo 3D excluído com sucesso.");
          await this.loadModelos3D(); // Recarregar os modelos
          this.$emit("pacienteChange"); // Atualizar o paciente
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir o modelo 3D. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir modelo 3D:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o modelo 3D.");
      }
    },

    confirmarExcluirTodosModelos(data) {
      if (!data) {
        cSwal.cError("Data inválida para exclusão de modelos.");
        return;
      }

      // Formatando a data para exibição
      const dataFormatada = this.$filters.dateLong(data);

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        `Tem certeza que deseja excluir todos os modelos 3D de <b>${dataFormatada}</b>?<br><br>Esta ação não pode ser desfeita.`,
        () => this.excluirTodosModelosPorData(data),
        {
          title: "Excluir todos os modelos",
          icon: "warning",
          confirmButtonText: "Sim, excluir todos",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirTodosModelosPorData(data) {
      if (!this.paciente || !this.paciente.id) {
        cSwal.cError("Paciente não identificado.");
        return;
      }

      cSwal.loading("Excluindo modelos 3D...");

      try {
        // Obter todos os modelos da data específica
        const modelosDaData = this.groupedModelosByDate.find(group => group.date === data)?.modelos || [];

        if (modelosDaData.length === 0) {
          cSwal.loaded();
          cSwal.cError("Nenhum modelo encontrado para esta data.");
          return;
        }

        // Excluir cada modelo individualmente
        for (const modelo of modelosDaData) {
          if (modelo.id) {
            await excluirModelo3D(modelo.id);
          }
        }

        cSwal.loaded();
        cSwal.cSuccess("Todos os modelos 3D foram excluídos com sucesso.");
        await this.loadModelos3D(); // Recarregar os modelos
        this.$emit("pacienteChange"); // Atualizar o paciente
      } catch (error) {
        console.error("Erro ao excluir modelos 3D por data:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir os modelos 3D.");
      }
    },

    /**
     * Carrega os modelos 3D do paciente
     */
    async loadModelos3D() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoadingModelos = true;

      try {
        // Usar o serviço para buscar os modelos 3D
        const modelos = await getModelos3D(this.paciente.id);

        // Atualizar o estado local
        this.modelos3d = modelos || [];

      } catch (error) {
        console.error('Erro ao carregar modelos 3D:', error);
        cSwal.cError('Erro ao carregar os modelos 3D. Por favor, tente novamente.');
      } finally {
        this.isLoadingModelos = false;
      }
    },

    onModeloDragStart(event, modelo) {
      if (!modelo || !modelo.id) return;

      // Definir o cursor de arrasto
      event.dataTransfer.effectAllowed = 'move';

      // Armazenar o ID do modelo sendo arrastado
      event.dataTransfer.setData('modelo-id', modelo.id);
      event.dataTransfer.setData('drag-type', 'modelo');

      // Ativar a zona de exclusão
      this.isDraggingModelo = true;

      // Adicionar uma imagem fantasma personalizada para o arrasto
      const ghostElement = document.createElement('div');
      ghostElement.classList.add('drag-ghost');
      ghostElement.innerHTML = `<i class="fas fa-cube"></i> ${modelo.descricao || 'Modelo 3D'}`;
      ghostElement.style.position = 'absolute';
      ghostElement.style.top = '-1000px';
      document.body.appendChild(ghostElement);
      event.dataTransfer.setDragImage(ghostElement, 20, 20);

      // Remover o elemento fantasma após um pequeno delay
      setTimeout(() => {
        document.body.removeChild(ghostElement);
      }, 100);
    },

    onGrupoDragStart(event, grupo) {
      if (!grupo || !grupo.grupo_exame_id) return;

      // Definir o cursor de arrasto
      event.dataTransfer.effectAllowed = 'move';

      // Armazenar o ID do grupo sendo arrastado
      event.dataTransfer.setData('grupo-id', grupo.grupo_exame_id);
      event.dataTransfer.setData('drag-type', 'grupo');

      // Ativar a zona de exclusão
      this.isDraggingModelo = true;

      // Adicionar uma imagem fantasma personalizada para o arrasto
      const ghostElement = document.createElement('div');
      ghostElement.classList.add('drag-ghost');
      ghostElement.innerHTML = `<i class="fas fa-cubes"></i> ${grupo.grupo_nome} (${grupo.modelos.length} modelos)`;
      ghostElement.style.position = 'absolute';
      ghostElement.style.top = '-1000px';
      document.body.appendChild(ghostElement);
      event.dataTransfer.setDragImage(ghostElement, 20, 20);

      // Remover o elemento fantasma após um pequeno delay
      setTimeout(() => {
        document.body.removeChild(ghostElement);
      }, 100);
    },

    // Métodos para gerenciar grupos de modelos
    toggleGrupoExpanded(grupoExameId) {
      const index = this.gruposExpandidos.indexOf(grupoExameId);
      if (index > -1) {
        this.gruposExpandidos.splice(index, 1);
      } else {
        this.gruposExpandidos.push(grupoExameId);
      }
    },

    isGrupoExpanded(grupoExameId) {
      return this.gruposExpandidos.includes(grupoExameId);
    },

    toggleModeloVisibility(modeloId) {
      const index = this.modelosVisiveis.indexOf(modeloId);
      if (index > -1) {
        this.modelosVisiveis.splice(index, 1);
      } else {
        this.modelosVisiveis.push(modeloId);
      }
    },

    isModeloVisible(modeloId) {
      return this.modelosVisiveis.includes(modeloId);
    },

    formatTipoModelo(tipo) {
      const tipos = {
        'mandibula': 'Mandíbula',
        'maxila': 'Maxila',
        'geral': 'Geral',
        'outro': 'Outro'
      };
      return tipos[tipo] || tipo || 'Geral';
    },

    async openGrupoViewer(grupo) {
      if (!grupo.modelos || grupo.modelos.length === 0) {
        cSwal.cError("Nenhum modelo encontrado neste grupo.");
        return;
      }

      // Configurar dados do grupo para o visualizador múltiplo
      this.currentGrupoViewer = {
        modelos: grupo.modelos,
        grupoNome: grupo.grupo_nome,
        grupoData: grupo.data,
        pacienteNome: this.paciente.nome
      };

      // Abrir modal do visualizador múltiplo
      this.openMultipleModelViewer();
    },

    openMultipleModelViewer() {
      const modalElement = document.getElementById('multipleModelViewerModal');

      // Adicionar backdrop
      let backdrop = document.querySelector('.modal-backdrop');
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);
        backdrop.offsetHeight;
        backdrop.classList.add('show');
      }

      // Mostrar modal
      modalElement.classList.add('show');
      modalElement.style.display = 'block';
      document.body.classList.add('modal-open');

      this.showMultipleViewer = true;
    },

    closeMultipleModelViewer() {
      const modalElement = document.getElementById('multipleModelViewerModal');

      // Remover backdrop
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.classList.remove('show');
        setTimeout(() => {
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
          }
        }, 150);
      }

      // Ocultar modal
      modalElement.classList.remove('show');
      modalElement.style.display = 'none';
      document.body.classList.remove('modal-open');

      this.showMultipleViewer = false;
      this.currentGrupoViewer = null;
    },

    handleMultipleViewerError(error) {
      console.error("Erro no visualizador múltiplo:", error);
      cSwal.cError("Erro ao carregar o grupo de modelos 3D.");
      this.closeMultipleModelViewer();
    },

    async confirmarExcluirGrupo(grupo) {
      const result = await cSwal.cConfirm(
        `Tem certeza que deseja excluir o grupo "${grupo.grupo_nome}" com ${grupo.modelos.length} modelo(s)?`,
        "Esta ação não pode ser desfeita!"
      );

      if (!result.isConfirmed) return;

      cSwal.loading("Excluindo grupo de modelos...");

      try {
        const success = await excluirGrupoModelos3D(grupo.grupo_exame_id);

        if (success) {
          cSwal.loaded();
          cSwal.cSuccess("Grupo de modelos excluído com sucesso.");
          await this.loadModelosAgrupados();
          this.$emit("pacienteChange");
        } else {
          throw new Error("Falha ao excluir grupo");
        }
      } catch (error) {
        console.error("Erro ao excluir grupo de modelos:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o grupo de modelos.");
      }
    },

    async loadModelosAgrupados() {
      if (!this.paciente?.id) return;

      try {
        this.isLoadingModelos = true;
        const modelosAgrupados = await getModelosAgrupados(this.paciente.id);
        this.modelosAgrupados = modelosAgrupados || [];
      } catch (error) {
        console.error("Erro ao carregar modelos agrupados:", error);
        this.modelosAgrupados = [];
      } finally {
        this.isLoadingModelos = false;
      }
    },

    // Métodos de controle do visualizador foram removidos pois agora usamos o componente Modelos3DViewer
  },
  async mounted() {
    // Add global event listeners for drag operations
    window.addEventListener('dragstart', () => {
      this.isDraggingModelo = true;
    });

    window.addEventListener('dragend', () => {
      this.isDraggingModelo = false;
    });

    // Carregar modelos 3D quando o componente for montado
    await this.loadModelos3D();

    // Carregar modelos agrupados se a funcionalidade estiver habilitada
    if (this.showGroups) {
      await this.loadModelosAgrupados();
    }
  },
  beforeUnmount() {
    // Clean up event listeners
    window.removeEventListener('dragstart', () => {
      this.isDraggingModelo = true;
    });

    window.removeEventListener('dragend', () => {
      this.isDraggingModelo = false;
    });

    // Remover evento de ESC
    document.removeEventListener('keydown', this.handleEscKey);

    // Fechar modal se estiver aberto
    const modalElement = document.getElementById('modelViewerModal');
    if (modalElement && modalElement.classList.contains('show')) {
      this.closeModelViewer();
    }
  }
};
</script>
