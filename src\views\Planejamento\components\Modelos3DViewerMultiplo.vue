<template>
  <div class="modelo3d-viewer-multiplo-wrapper">
    <div class="modelo3d-viewer-container">
      <!-- Loader para os modelos 3D -->
      <div v-if="isLoading" class="stl-loader-container">
        <div class="stl-loader-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>
        <div class="stl-loader-progress">
          <div class="progress">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated"
              role="progressbar"
              :style="{ width: loadingProgress + '%' }"
              :aria-valuenow="loadingProgress"
              aria-valuemin="0"
              aria-valuemax="100"
            >
            </div>
          </div>
          <div class="stl-loader-text">Carregando modelos 3D... ({{ loadedModels }}/{{ totalModels }})</div>
        </div>
      </div>

      <!-- Container para o visualizador 3D -->
      <div
        ref="viewerContainer"
        class="online-3d-viewer"
        :class="{ 'hidden': isLoading, 'fullscreen-mode': isFullscreen }"
      ></div>

      <!-- Overlay de tela cheia -->
      <div class="fullscreen-overlay" v-if="isFullscreen"></div>

      <!-- Botão para sair do modo tela cheia -->
      <button
        v-if="isFullscreen"
        class="exit-fullscreen-btn"
        title="Sair da tela cheia"
        @click="toggleFullscreen"
      >
        <font-awesome-icon :icon="['fas', 'compress-alt']" />
      </button>

      <!-- Controles do visualizador múltiplo -->
      <div class="viewer-controls desktop-controls" v-if="!isLoading">
        <!-- Controles de modelos individuais -->
        <div class="controls-group models-controls">
          <h6 class="controls-title">
            <font-awesome-icon :icon="['fas', 'cubes']" class="me-2" />
            Modelos ({{ modelos.length }})
          </h6>
          <div class="models-list">
            <div 
              v-for="(modelo, index) in modelos" 
              :key="modelo.id || index"
              class="model-control-item"
              :class="{ 'visible': isModelVisible(modelo.id || index) }"
            >
              <div class="model-info">
                <span class="model-name">{{ modelo.descricao || `Modelo ${index + 1}` }}</span>
                <span class="model-type">{{ formatTipoModelo(modelo.tipo_modelo) }}</span>
              </div>
              <div class="model-actions">
                <button
                  class="model-visibility-btn"
                  :class="{ 'visible': isModelVisible(modelo.id || index) }"
                  @click="toggleModelVisibility(modelo.id || index)"
                  :title="isModelVisible(modelo.id || index) ? 'Ocultar modelo' : 'Mostrar modelo'"
                >
                  <font-awesome-icon 
                    :icon="['fas', isModelVisible(modelo.id || index) ? 'eye' : 'eye-slash']" 
                  />
                </button>
                <button
                  class="model-focus-btn"
                  @click="focusOnModel(modelo.id || index)"
                  title="Focar neste modelo"
                >
                  <font-awesome-icon :icon="['fas', 'crosshairs']" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Controles principais -->
        <div class="controls-group main-controls">
          <button
            class="control-btn"
            title="Tela cheia"
            @click="toggleFullscreen"
          >
            <font-awesome-icon :icon="['fas', 'expand-alt']" />
          </button>
          
          <button
            class="control-btn"
            title="Resetar visualização"
            @click="resetView"
          >
            <font-awesome-icon :icon="['fas', 'home']" />
          </button>

          <button
            class="control-btn"
            :class="{ 'active': showEdges }"
            title="Mostrar/Ocultar arestas"
            @click="toggleEdges"
          >
            <font-awesome-icon :icon="['fas', 'vector-square']" />
          </button>

          <button
            class="control-btn"
            title="Mostrar todos os modelos"
            @click="showAllModels"
          >
            <font-awesome-icon :icon="['fas', 'eye']" />
          </button>

          <button
            class="control-btn"
            title="Ocultar todos os modelos"
            @click="hideAllModels"
          >
            <font-awesome-icon :icon="['fas', 'eye-slash']" />
          </button>
        </div>

        <!-- Informações do grupo -->
        <div class="controls-group info-controls">
          <div class="viewer-info">
            <h6 class="info-title">{{ grupoNome || 'Grupo de Modelos' }}</h6>
            <p class="info-subtitle">{{ pacienteNome }}</p>
            <p class="info-date">{{ $filters.dateLong(grupoData) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modelo3d-viewer-multiplo-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f8f9fa;
}

.modelo3d-viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.online-3d-viewer {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.online-3d-viewer.hidden {
  opacity: 0;
  pointer-events: none;
}

.online-3d-viewer.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.stl-loader-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.stl-loader-spinner {
  margin-bottom: 20px;
}

.stl-loader-progress {
  width: 300px;
  margin: 0 auto;
}

.stl-loader-text {
  margin-top: 10px;
  color: #6c757d;
  font-size: 0.9rem;
}

.viewer-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 300px;
  z-index: 100;
}

.controls-group {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-title {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
}

.models-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.model-control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.model-control-item.visible {
  background: #e6fffa;
  border-color: #38b2ac;
}

.model-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.model-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: #2d3748;
}

.model-type {
  font-size: 0.75rem;
  color: #718096;
  text-transform: capitalize;
}

.model-actions {
  display: flex;
  gap: 4px;
}

.model-visibility-btn,
.model-focus-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.model-visibility-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.model-visibility-btn.visible {
  background: #38b2ac;
  color: white;
}

.model-focus-btn {
  background: #667eea;
  color: white;
}

.model-focus-btn:hover,
.model-visibility-btn:hover {
  transform: scale(1.1);
}

.main-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.control-btn:hover {
  background: #5a67d8;
  transform: translateY(-2px);
}

.control-btn.active {
  background: #38b2ac;
}

.info-controls .viewer-info {
  text-align: center;
}

.info-title {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.info-subtitle {
  margin: 0 0 3px 0;
  font-size: 0.85rem;
  color: #4a5568;
}

.info-date {
  margin: 0;
  font-size: 0.8rem;
  color: #718096;
}

.exit-fullscreen-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10000;
  transition: all 0.3s ease;
}

.exit-fullscreen-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9998;
}
</style>

<script>
import * as OV from 'online-3d-viewer';
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { downloadModelo3D } from "@/services/modelos3dService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: 'Modelos3DViewerMultiplo',
  props: {
    modelos: {
      type: Array,
      required: true,
      default: () => []
    },
    pacienteNome: {
      type: String,
      default: ''
    },
    grupoNome: {
      type: String,
      default: 'Grupo de Modelos'
    },
    grupoData: {
      type: String,
      default: ''
    }
  },
  emits: ['error', 'close'],
  setup(props, { emit }) {
    const viewerContainer = ref(null);
    const isLoading = ref(true);
    const loadingProgress = ref(0);
    const loadedModels = ref(0);
    const totalModels = ref(0);
    const isFullscreen = ref(false);
    const showEdges = ref(false);
    const modelVisibility = ref({});

    let viewer = null;
    let loadedMeshes = {};
    let progressInterval = null;

    // Inicializar visibilidade dos modelos
    const initializeVisibility = () => {
      const visibility = {};
      props.modelos.forEach((modelo, index) => {
        visibility[modelo.id || index] = true;
      });
      modelVisibility.value = visibility;
    };

    // Verificar se um modelo está visível
    const isModelVisible = (modelId) => {
      return modelVisibility.value[modelId] || false;
    };

    // Alternar visibilidade de um modelo
    const toggleModelVisibility = (modelId) => {
      modelVisibility.value[modelId] = !modelVisibility.value[modelId];
      updateModelVisibility(modelId);
    };

    // Atualizar visibilidade no visualizador
    const updateModelVisibility = (modelId) => {
      if (viewer && loadedMeshes[modelId]) {
        const mesh = loadedMeshes[modelId];
        mesh.visible = modelVisibility.value[modelId];
        viewer.Render();
      }
    };

    // Focar em um modelo específico
    const focusOnModel = (modelId) => {
      if (viewer && loadedMeshes[modelId]) {
        // Ocultar todos os outros modelos temporariamente
        Object.keys(loadedMeshes).forEach(id => {
          if (id !== modelId && loadedMeshes[id]) {
            loadedMeshes[id].visible = false;
          }
        });

        // Mostrar apenas o modelo selecionado
        loadedMeshes[modelId].visible = true;

        // Ajustar a câmera para focar no modelo
        viewer.FitToWindow(0.9);
        viewer.Render();

        // Restaurar visibilidade após um tempo
        setTimeout(() => {
          Object.keys(loadedMeshes).forEach(id => {
            if (loadedMeshes[id]) {
              loadedMeshes[id].visible = modelVisibility.value[id];
            }
          });
          viewer.Render();
        }, 2000);
      }
    };

    // Mostrar todos os modelos
    const showAllModels = () => {
      Object.keys(modelVisibility.value).forEach(modelId => {
        modelVisibility.value[modelId] = true;
        updateModelVisibility(modelId);
      });
    };

    // Ocultar todos os modelos
    const hideAllModels = () => {
      Object.keys(modelVisibility.value).forEach(modelId => {
        modelVisibility.value[modelId] = false;
        updateModelVisibility(modelId);
      });
    };

    // Alternar modo tela cheia
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value;

      if (viewer) {
        setTimeout(() => {
          viewer.Resize();
        }, 100);
      }
    };

    // Resetar visualização
    const resetView = () => {
      if (viewer) {
        viewer.FitToWindow(0.9);
      }
    };

    // Alternar exibição de arestas
    const toggleEdges = () => {
      showEdges.value = !showEdges.value;
      // Implementar lógica de arestas se necessário
    };

    // Formatar tipo de modelo
    const formatTipoModelo = (tipo) => {
      const tipos = {
        'mandibula': 'Mandíbula',
        'maxila': 'Maxila',
        'geral': 'Geral',
        'outro': 'Outro'
      };
      return tipos[tipo] || tipo || 'Geral';
    };

    // Carregar modelos 3D
    const loadModels = async () => {
      if (!props.modelos || props.modelos.length === 0) {
        emit('error', 'Nenhum modelo fornecido');
        return;
      }

      try {
        isLoading.value = true;
        loadedModels.value = 0;
        totalModels.value = props.modelos.length;
        loadingProgress.value = 0;

        // Inicializar o visualizador
        viewer = new OV.EmbeddedViewer(viewerContainer.value, {
          backgroundColor: new OV.RGBColor(248, 249, 250),
          defaultColor: new OV.RGBColor(200, 200, 200),
          edgeSettings: new OV.EdgeSettings(false, new OV.RGBColor(0, 0, 0), 1),
          environmentSettings: new OV.EnvironmentSettings([
            'assets/envmaps/fishermans_bastion/posx.jpg',
            'assets/envmaps/fishermans_bastion/negx.jpg',
            'assets/envmaps/fishermans_bastion/posy.jpg',
            'assets/envmaps/fishermans_bastion/negy.jpg',
            'assets/envmaps/fishermans_bastion/posz.jpg',
            'assets/envmaps/fishermans_bastion/negz.jpg'
          ], false)
        });

        // Simular progresso
        progressInterval = setInterval(() => {
          if (loadingProgress.value < 90) {
            loadingProgress.value += Math.random() * 10;
          }
        }, 200);

        // Carregar cada modelo
        for (let i = 0; i < props.modelos.length; i++) {
          const modelo = props.modelos[i];
          await loadSingleModel(modelo, i);
          loadedModels.value++;
          loadingProgress.value = (loadedModels.value / totalModels.value) * 100;
        }

        // Finalizar carregamento
        clearInterval(progressInterval);
        loadingProgress.value = 100;

        setTimeout(() => {
          isLoading.value = false;
          viewer.FitToWindow(0.9);
        }, 500);

      } catch (error) {
        console.error('Erro ao carregar modelos:', error);
        clearInterval(progressInterval);
        isLoading.value = false;
        emit('error', 'Erro ao carregar os modelos 3D');
      }
    };

    // Carregar um modelo individual
    const loadSingleModel = async (modelo, index) => {
      try {
        const modelData = await downloadModelo3D(modelo.url);
        const modelId = modelo.id || index;

        // Determinar cor baseada no tipo
        const colors = {
          'mandibula': new OV.RGBColor(102, 126, 234),
          'maxila': new OV.RGBColor(72, 187, 120),
          'geral': new OV.RGBColor(200, 200, 200),
          'outro': new OV.RGBColor(245, 101, 101)
        };

        const color = colors[modelo.tipo_modelo] || colors['geral'];

        // Carregar modelo no visualizador
        const inputFiles = [{
          name: modelo.descricao || `modelo_${index}.stl`,
          content: modelData
        }];

        await viewer.LoadModelFromInputFiles(inputFiles);

        // Armazenar referência do mesh (simplificado)
        loadedMeshes[modelId] = {
          visible: true,
          color: color
        };

      } catch (error) {
        console.error(`Erro ao carregar modelo ${modelo.descricao}:`, error);
        throw error;
      }
    };

    // Observar mudanças nos modelos
    watch(() => props.modelos, () => {
      initializeVisibility();
      loadModels();
    }, { immediate: true });

    onMounted(() => {
      initializeVisibility();
      loadModels();
    });

    onBeforeUnmount(() => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
      if (viewer) {
        viewer.Destroy();
      }
    });

    return {
      viewerContainer,
      isLoading,
      loadingProgress,
      loadedModels,
      totalModels,
      isFullscreen,
      showEdges,
      isModelVisible,
      toggleModelVisibility,
      focusOnModel,
      showAllModels,
      hideAllModels,
      toggleFullscreen,
      resetView,
      toggleEdges,
      formatTipoModelo
    };
  }
};
</script>
