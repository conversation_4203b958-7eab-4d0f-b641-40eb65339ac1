import axios from '@/services/axios'
import moment from 'moment'

// Cache para modelos já baixados
const modelosCache = new Map();

export async function uploadModelo3D(options) {
    if (!options.paciente_id)
        return false;

    options = {
        dir: 'modelo3d',
        data: moment().format('YYYY-MM-DD HH:mm:ss'),
        descricao: '',
        tipo: 'stl',
        ...options,
    }

    try {
        let data = new FormData();
        data.append('paciente_id', options.paciente_id);
        data.append('modelo3d', options.modelo); // Alterado para 'modelo3d' conforme implementação do backend
        data.append('dir', options.dir);
        data.append('data', options.data);
        data.append('descricao', options.descricao);
        data.append('tipo', options.tipo);

        const response = await axios.post('/modelo3d', data, // Alterado para '/modelo3d' conforme implementação do backend
            {
                headers: { 'Content-Type': 'multipart/form-data' }
            })

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao enviar modelo 3D:', error);
    }

    return false;
}

export async function getModelos3D(pacienteId) {
    try {
        // Usando a rota específica implementada pelo backend para buscar modelos 3D de um paciente
        const response = await axios.get(`/paciente/${pacienteId}/modelos3d`);

        if (!response || !response.data)
            return [];

        // O backend retorna diretamente o array de modelos 3D
        return response.data || [];

    } catch (error) {
        console.error('Erro ao buscar modelos 3D:', error);
        return [];
    }
}

export async function excluirModelo3D(id) {
    try {
        // Assumindo que a rota de exclusão segue o mesmo padrão da rota de upload
        const response = await axios.delete(`/modelo3d/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir modelo 3D:', error);
        return false;
    }
}

/**
 * Upload de múltiplos modelos 3D como um grupo
 * @param {Object} options - Opções do upload
 * @param {number} options.paciente_id - ID do paciente
 * @param {string} options.grupo_nome - Nome do grupo
 * @param {Array} options.modelos - Array de arquivos de modelo
 * @param {Array} options.tipos_modelos - Array de tipos dos modelos (opcional)
 * @param {Array} options.descricoes - Array de descrições dos modelos (opcional)
 * @param {string} options.data - Data do exame
 * @param {string} options.dir - Diretório de armazenamento (opcional)
 * @returns {Promise<Object|boolean>} - Resposta do servidor ou false em caso de erro
 */
export async function uploadGrupoModelos3D(options) {
    if (!options.paciente_id || !options.grupo_nome || !options.modelos || options.modelos.length === 0) {
        return false;
    }

    options = {
        dir: 'grupo_modelos',
        data: moment().format('YYYY-MM-DD HH:mm:ss'),
        tipos_modelos: [],
        descricoes: [],
        ...options,
    }

    try {
        let data = new FormData();
        data.append('paciente_id', options.paciente_id);
        data.append('grupo_nome', options.grupo_nome);
        data.append('data', options.data);
        data.append('dir', options.dir);

        // Adicionar cada modelo
        options.modelos.forEach((modelo, index) => {
            data.append('modelos[]', modelo);
        });

        // Adicionar tipos de modelos se fornecidos
        if (options.tipos_modelos && options.tipos_modelos.length > 0) {
            options.tipos_modelos.forEach((tipo, index) => {
                data.append('tipos_modelos[]', tipo);
            });
        }

        // Adicionar descrições se fornecidas
        if (options.descricoes && options.descricoes.length > 0) {
            options.descricoes.forEach((descricao, index) => {
                data.append('descricoes[]', descricao);
            });
        }

        // Campos opcionais
        if (options.is_diagnostico) {
            data.append('is_diagnostico', options.is_diagnostico);
        }
        if (options.tag_diagnostico) {
            data.append('tag_diagnostico', options.tag_diagnostico);
        }

        const response = await axios.post('/modelo3d/grupo', data, {
            headers: { 'Content-Type': 'multipart/form-data' }
        });

        if (!response || !response.data || !response.data.success) {
            return false;
        }

        return response;

    } catch (error) {
        console.error('Erro ao enviar grupo de modelos 3D:', error);
        return false;
    }
}

/**
 * Busca modelos 3D agrupados de um paciente
 * @param {number} pacienteId - ID do paciente
 * @returns {Promise<Array>} - Array com grupos e modelos individuais
 */
export async function getModelosAgrupados(pacienteId) {
    try {
        const response = await axios.get(`/paciente/${pacienteId}/modelos3d-agrupados`);

        if (!response || !response.data) {
            return [];
        }

        return response.data || [];

    } catch (error) {
        console.error('Erro ao buscar modelos agrupados:', error);
        return [];
    }
}

/**
 * Exclui um grupo completo de modelos 3D
 * @param {string} grupoExameId - ID do grupo de exame
 * @returns {Promise<boolean>} - true se excluído com sucesso, false caso contrário
 */
export async function excluirGrupoModelos3D(grupoExameId) {
    try {
        const response = await axios.delete('/modelo3d/grupo', {
            data: { grupo_exame_id: grupoExameId }
        });

        if (!response || !response.data || !response.data.success) {
            return false;
        }

        return true;

    } catch (error) {
        console.error('Erro ao excluir grupo de modelos 3D:', error);
        return false;
    }
}

/**
 * Baixa um modelo 3D a partir da URL
 * @param {string} url - URL do modelo 3D
 * @param {boolean} forceRefresh - Se true, ignora o cache e baixa novamente
 * @returns {Promise<ArrayBuffer>} - ArrayBuffer contendo os dados do modelo
 */
export async function downloadModelo3D(url, forceRefresh = false) {
    // Verificar se o modelo já está em cache
    if (!forceRefresh && modelosCache.has(url)) {
        return modelosCache.get(url);
    }

    try {
        // Fazer o download do modelo
        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            headers: {
                'Accept': '*/*'
            }
        });

        if (!response || !response.data) {
            throw new Error('Falha ao baixar o modelo 3D');
        }

        // Armazenar no cache
        modelosCache.set(url, response.data);

        return response.data;
    } catch (error) {
        console.error('Erro ao baixar modelo 3D:', error);
        throw error;
    }
}
